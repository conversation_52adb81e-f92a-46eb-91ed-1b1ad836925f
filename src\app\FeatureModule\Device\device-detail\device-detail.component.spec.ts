import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject } from 'rxjs';
import { TransferOrderModuleComponent } from '../../TransferOrder/transfer-order-module/transfer-order-module.component';
import { commonsProviders } from '../../../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { TransferProductDetails } from '../../../model/device/TransferProductDetails.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { RoleApiCallService } from '../../../shared/Service/RoleService/role-api-call.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { PermissionService } from '../../../shared/permission.service';
import { BooleanKeyValueMappingDisplayNamePipe } from '../../../shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { HidePermissionNamePipe } from '../../../shared/pipes/Role/hidePermissionName.pipe';
import { DeviceTypeNamePipe } from '../../../shared/pipes/device-type-name.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ValidationService } from '../../../shared/util/validation.service';
import { DeviceDetailComponent } from './device-detail.component';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { deviceTypesEnum } from '../../../shared/enum/deviceTypesEnum.enum';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';


describe('DeviceDetailComponent', () => {
  let component: DeviceDetailComponent;
  let fixture: ComponentFixture<DeviceDetailComponent>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let customerAssociationServiceSpy: jasmine.SpyObj<CustomerAssociationService>;
  let validationServiceSpy: jasmine.SpyObj<ValidationService>;
  let confirmDialogServiceSpy: jasmine.SpyObj<ConfirmDialogService>;
  let commonOperationsServiceSpy: jasmine.SpyObj<CommonOperationsService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;
  let enumMappingDisplayNamePipeSpy: jasmine.SpyObj<EnumMappingDisplayNamePipe>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    deviceOperationServiceSpy = jasmine.createSpyObj('DeviceOperationService', [
      'loadDeviceDetail', 'getReleaseVersions', 'assignReleaseVersion', 'shouldDisableAssignButton',
      'associateDevicesWithSalesOrder', 'enableDisableDevices', 'lockUnlockDevices',
      'convertDevicesToTest', 'convertDevicesToDemo', 'convertDevicesToClient',
      'disableProductStatusForDevices', 'rmaProductStatusForDevices', 'validateSingleDevicePermissions',
      'validateSingleDeviceCountryAccess', 'handleDeviceOperation'
    ]);

    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getDevicePermission']);
    customerAssociationServiceSpy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);
    validationServiceSpy = jasmine.createSpyObj('ValidationService', ['validateProductStatusForRMAAction']);
    confirmDialogServiceSpy = jasmine.createSpyObj('ConfirmDialogService', [
      'confirm', 'getBasicModelConfigForDisableAction', 'getBasicModelConfigForRMAAction', 'getErrorMessageDisableToRma'
    ]);
    commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', [
      'accessDeviceListOperations', 'getCommonLoadingSubject'
    ]);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', [
      'enumOptionToList', 'lockedUnlockOptionList', 'editEnableDisableOptionList'
    ]);
    enumMappingDisplayNamePipeSpy = jasmine.createSpyObj('EnumMappingDisplayNamePipe', ['transform']);

    // Setup mock return values
    countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve([]));
    permissionServiceSpy.getDevicePermission.and.returnValue(true);
    keyValueMappingServiceSpy.enumOptionToList.and.returnValue([]);
    keyValueMappingServiceSpy.lockedUnlockOptionList.and.returnValue([]);
    keyValueMappingServiceSpy.editEnableDisableOptionList.and.returnValue([]);
    commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(new Subject<boolean>());
    commonOperationsServiceSpy.accessDeviceListOperations.and.returnValue([]);

    await TestBed.configureTestingModule({
      declarations: [DeviceDetailComponent, TransferOrderModuleComponent, EnumMappingDisplayNamePipe, DeviceTypeNamePipe, BooleanKeyValueMappingDisplayNamePipe],
      imports: [],
      providers: [
        DeviceService,
        DownloadService,
        CommonsService,
        LocalStorageService,
        DatePipe,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        SSOLoginService,
        RoleApiCallService,
        HidePermissionNamePipe,
        PrintListPipe,
        { provide: DeviceOperationService, useValue: deviceOperationServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationServiceSpy },
        { provide: ValidationService, useValue: validationServiceSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        { provide: EnumMappingDisplayNamePipe, useValue: enumMappingDisplayNamePipeSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component with default values and call required services', async () => {
      // Arrange
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      spyOn(component as any, 'subjectInit').and.stub();

      // Act
      await component.ngOnInit();

      // Assert
      expect(component.loading).toBe(false);
      expect(countryCacheServiceSpy.getCountryListFromCache).toHaveBeenCalledWith(false);
      expect(permissionServiceSpy.getDevicePermission).toHaveBeenCalledWith(PermissionAction.UPDATE_DEVICE_TYPE_ACTION);
      expect(keyValueMappingServiceSpy.enumOptionToList).toHaveBeenCalledWith(ProductStatusEnum);
      expect(keyValueMappingServiceSpy.lockedUnlockOptionList).toHaveBeenCalled();
      expect(keyValueMappingServiceSpy.editEnableDisableOptionList).toHaveBeenCalled();
      expect(component.deviceDetailDisplay).toBe(true);
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
      expect((component as any).subjectInit).toHaveBeenCalled();
    });


  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from subscription if it exists', () => {
      // Arrange
      const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForCommonloading = mockSubscription;

      // Act
      component.ngOnDestroy();

      // Assert
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });


  });

  describe('subjectInit', () => {
    it('should subscribe to common loading subject', () => {
      // Arrange
      const mockSubject = new Subject<boolean>();
      commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(mockSubject);

      // Act
      (component as any).subjectInit();

      // Assert
      expect(commonOperationsServiceSpy.getCommonLoadingSubject).toHaveBeenCalled();
      expect(component.subscriptionForCommonloading).toBeDefined();
    });


  });

  describe('deviceDetailModel', () => {
    it('should call DeviceOperationService.loadDeviceDetail and handle success', async () => {
      // Arrange
      const mockDeviceId = 123;
      const mockDeviceDetail = {
        id: 123,
        deviceId: 'DEV001',
        deviceType: deviceTypesEnum.TEST_DEVICE,
        countryId: 1,
        packageVersion: '1.0.0',
        orderRecordType: 'SALES_ORDER'
      } as any;
      const mockTransferDetails = new TransferProductDetails(123, 456, 'SN001', 'TEST_DEVICE', 'Customer1');
      const mockResult = {
        success: true,
        deviceDetail: mockDeviceDetail,
        releaseVersionId: 789,
        transferProductDetails: mockTransferDetails
      };
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.resolve(mockResult));
      spyOn(component as any, 'getReleaseVersions').and.returnValue(Promise.resolve());

      // Act
      await component.deviceDetailModel(mockDeviceId);

      // Assert
      expect(deviceOperationServiceSpy.loadDeviceDetail).toHaveBeenCalledWith(mockDeviceId);
      expect(component.deviceDetailResponse).toEqual(mockDeviceDetail);
      expect(component.releaseVersionId).toBe(789);
      expect(component.deviceDetailsTrasferProduct).toEqual(mockTransferDetails);
      expect((component as any).getReleaseVersions).toHaveBeenCalledWith(
        mockDeviceDetail.deviceType,
        mockDeviceDetail.countryId,
        mockDeviceDetail.packageVersion
      );
      expect(commonOperationsServiceSpy.accessDeviceListOperations).toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });



    it('should handle DeviceOperationService.loadDeviceDetail failure and show warning', async () => {
      // Arrange
      const mockDeviceId = 123;
      const mockResult = {
        success: false,
        deviceDetail: null,
        releaseVersionId: -1,
        transferProductDetails: null
      };
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.resolve(mockResult));
      spyOn(component, 'back').and.stub();

      // Act
      await component.deviceDetailModel(mockDeviceId);

      // Assert
      expect(toastrServiceMock.warning).toHaveBeenCalled();
      expect(component.back).toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('assignReleaseVersion', () => {
    it('should call DeviceOperationService.assignReleaseVersion', async () => {
      // Arrange
      component.deviceDetailResponse = { id: 123 } as any;
      component.selectedReleaseVersion = 456;
      component.deviceIdInput = 123;
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.assignReleaseVersion.and.returnValue(Promise.resolve());
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      await component.assignReleaseVersion();

      // Assert
      expect(deviceOperationServiceSpy.validateSingleDevicePermissions).toHaveBeenCalled();
      expect(deviceOperationServiceSpy.assignReleaseVersion).toHaveBeenCalledWith(123, 456);
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });


  });

  describe('changeReleaseVersion', () => {
    beforeEach(() => {
      component.releaseVersions = [
        { id: 456, itemNumber: 'ITEM456' },
        { id: 789, itemNumber: 'ITEM789' }
      ];
      component.releaseVersionId = 123;
    });

    it('should update selectedReleaseVersion when valid item found', () => {
      // Arrange
      const mockEvent = { target: { value: '456' } };
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(false);

      // Act
      component.changeReleaseVersion(mockEvent);

      // Assert
      expect(component.selectedReleaseVersion).toBe(456);
      expect(deviceOperationServiceSpy.shouldDisableAssignButton).toHaveBeenCalledWith(456, 123);
      expect(component.btnReleaseVersionDisable).toBe(false);
    });


  });



  describe('back', () => {
    it('should emit showDevice event', () => {
      // Arrange
      spyOn(component.showDevice, 'emit');

      // Act
      component.back();

      // Assert
      expect(component.showDevice.emit).toHaveBeenCalled();
    });
  });

  describe('getReleaseVersions', () => {
    it('should get release versions successfully', async () => {
      // Arrange
      const mockResult = {
        success: true,
        releaseVersions: [{ id: 1, itemNumber: 'ITEM1' }],
        selectedReleaseVersion: 1,
        btnReleaseVersionDisable: false
      };
      deviceOperationServiceSpy.getReleaseVersions.and.returnValue(Promise.resolve(mockResult));
      component.releaseVersionId = 123;

      // Act
      await (component as any).getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, '1.0.0');

      // Assert
      expect(deviceOperationServiceSpy.getReleaseVersions).toHaveBeenCalledWith(
        deviceTypesEnum.TEST_DEVICE, 1, '1.0.0', component.updateDeviceTypePermission
      );
      expect(component.releaseVersions).toEqual([{ id: 1, itemNumber: 'ITEM1' }]);
      expect(component.selectedReleaseVersion).toBe(123);
      expect(deviceOperationServiceSpy.shouldDisableAssignButton).toHaveBeenCalledWith(123, 123);
    });


  });







  describe('validateProductStatusForDisableAction', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123 } as any;
    });

    it('should call disableProductStatusForDevice when validation passes', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      spyOn(component as any, 'disableProductStatusForDevice').and.returnValue(Promise.resolve());
      await (component as any).validateProductStatusForDisableAction();
      expect((component as any).disableProductStatusForDevice).toHaveBeenCalled();
    });
  });

  describe('enableDisableDevice', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, editable: false } as any;
      component.deviceIdInput = 123;
    });

    it('should enable device when validation passes', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.enableDisableDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      await (component as any).enableDisableDevice(true);
      expect(component.loading).toBe(false);
    });
  });

  describe('lockUnlock', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, locked: false } as any;
      component.deviceIdInput = 123;
    });

    it('should lock device when validation passes', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.lockUnlockDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      await component.lockUnlock(true);
      expect(component.loading).toBe(false);
    });
  });

  describe('convertDataToTest', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, deviceType: deviceTypesEnum.CLIENT_DEVICE } as any;
      component.deviceIdInput = 123;
    });

    it('should convert device to test when validation passes', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.convertDevicesToTest.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      await component.convertDataToTest();
      expect(component.loading).toBe(false);
    });

    it('should handle service error', async () => {
      deviceOperationServiceSpy.convertDevicesToTest.and.returnValue(Promise.reject(new Error('Service error')));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      try {
        await component.convertDataToTest();
      } catch (error) {
        // Expected to throw
      }

      expect(component.loading).toBe(false);
    });
  });

  describe('convertDataToDemo', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, deviceType: deviceTypesEnum.CLIENT_DEVICE } as any;
      component.deviceIdInput = 123;
    });

    it('should convert device to demo when validation passes', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.convertDevicesToDemo.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      await component.convertDataToDemo();
      expect(component.loading).toBe(false);
    });

    it('should handle service error', async () => {
      deviceOperationServiceSpy.convertDevicesToDemo.and.returnValue(Promise.reject(new Error('Service error')));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      try {
        await component.convertDataToDemo();
      } catch (error) {
        // Expected to throw
      }

      expect(component.loading).toBe(false);
    });
  });

  describe('convertDataToClient', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, deviceType: deviceTypesEnum.TEST_DEVICE } as any;
      component.deviceIdInput = 123;
    });

    it('should convert device to client when validation passes', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.convertDevicesToClient.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      await component.convertDataToClient();
      expect(component.loading).toBe(false);
    });

  });



  describe('validateProductStatusForRMAAction', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, productStatus: ProductStatusEnum.ENABLED } as any;
      component.productStatusList = [{ key: ProductStatusEnum.ENABLED, value: 'Enabled' }] as any;
    });

    it('should call rmaProductStatusForDevice when product status validation passes', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      enumMappingDisplayNamePipeSpy.transform.and.returnValue('Enabled');
      validationServiceSpy.validateProductStatusForRMAAction.and.returnValue(true);
      spyOn(component as any, 'rmaProductStatusForDevice').and.returnValue(Promise.resolve());
      await (component as any).validateProductStatusForRMAAction();
      expect((component as any).rmaProductStatusForDevice).toHaveBeenCalled();
    });

    it('should call rmaProductStatusForDevice directly', async () => {
      spyOn(component as any, 'rmaProductStatusForDevice').and.returnValue(Promise.resolve());
      await (component as any).validateProductStatusForRMAAction();
      expect((component as any).rmaProductStatusForDevice).toHaveBeenCalled();
    });
  });

  describe('disableProductStatusForDevice', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123 } as any;
      component.deviceIdInput = 123;
    });

    it('should disable device when user confirms', async () => {
      const mockConfig = { title: 'Confirm', message: 'Are you sure?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForDisableAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(true));
      deviceOperationServiceSpy.disableProductStatusForDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      await (component as any).disableProductStatusForDevice();
      expect(component.loading).toBe(false);
    });

    it('should not disable device when user cancels', async () => {
      const mockConfig = { title: 'Confirm', message: 'Are you sure?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForDisableAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(false));
      await (component as any).disableProductStatusForDevice();
      expect(deviceOperationServiceSpy.disableProductStatusForDevices).not.toHaveBeenCalled();
    });
  });

  describe('rmaProductStatusForDevice', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123 } as any;
      component.deviceIdInput = 123;
    });

    it('should set RMA status when user confirms', async () => {
      const mockConfig = { title: 'Confirm RMA', message: 'Set to RMA?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForRMAAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(true));
      deviceOperationServiceSpy.rmaProductStatusForDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      await (component as any).rmaProductStatusForDevice();
      expect(component.loading).toBe(false);
    });

    it('should not set RMA status when user cancels', async () => {
      const mockConfig = { title: 'Confirm RMA', message: 'Set to RMA?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForRMAAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(false));
      await (component as any).rmaProductStatusForDevice();
      expect(deviceOperationServiceSpy.rmaProductStatusForDevices).not.toHaveBeenCalled();
    });
  });

  describe('transferOrderSelectionToggle', () => {
    it('should toggle display states and call deviceDetailModel when deviceDetailDisplay is true', () => {
      // Arrange
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      component.transferOrderSelectionToggle(true, false);

      // Assert
      expect(component.deviceDetailDisplay).toBe(true);
      expect(component.transferOrderSelectionDisaplay).toBe(false);
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });

    it('should toggle display states without calling deviceDetailModel when deviceDetailDisplay is false', () => {
      // Arrange
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      component.transferOrderSelectionToggle(false, true);

      // Assert
      expect(component.deviceDetailDisplay).toBe(false);
      expect(component.transferOrderSelectionDisaplay).toBe(true);
      expect(component.deviceDetailModel).not.toHaveBeenCalled();
    });
  });

  describe('refreshDeviceDetailPage', () => {
    it('should call deviceDetailModel with deviceIdInput', () => {
      // Arrange
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      component.refreshDeviceDetailPage();

      // Assert
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });
  });

  describe('changeDeviceOperation', () => {
    it('should call deviceOperationService.handleDeviceOperation', () => {
      const mockEvent = { target: { value: 'TEST_OPERATION' } };
      component.changeDeviceOperation(mockEvent);
      expect(deviceOperationServiceSpy.handleDeviceOperation).toHaveBeenCalledWith('TEST_OPERATION', component, 'deviceOperation');
    });
  });

  // ==================== COMPREHENSIVE TESTS FOR 100% COVERAGE ====================

  describe('getReleaseVersions - comprehensive', () => {
    it('should handle getReleaseVersions failure', async () => {
      deviceOperationServiceSpy.getReleaseVersions.and.returnValue(Promise.resolve({
        success: false,
        releaseVersions: [],
        selectedReleaseVersion: -1,
        btnReleaseVersionDisable: true
      }));
      spyOn(component as any, 'setEmptyReleaseVersions');

      await (component as any).getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, '1.0.0');

      expect((component as any).setEmptyReleaseVersions).toHaveBeenCalled();
    });

    it('should handle getReleaseVersions error', async () => {
      deviceOperationServiceSpy.getReleaseVersions.and.returnValue(Promise.reject(new Error('API Error')));
      spyOn(component as any, 'setEmptyReleaseVersions');

      await (component as any).getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, '1.0.0');

      expect((component as any).setEmptyReleaseVersions).toHaveBeenCalled();
    });
  });

  describe('setEmptyReleaseVersions', () => {
    it('should set empty release versions and toggle assign button', () => {
      spyOn(component as any, 'toggleAssignButton');

      (component as any).setEmptyReleaseVersions();

      expect(component.releaseVersions).toEqual([]);
      expect(component.selectedReleaseVersion).toBe(-1);
      expect((component as any).toggleAssignButton).toHaveBeenCalled();
    });
  });

  describe('toggleAssignButton', () => {
    it('should call shouldDisableAssignButton', () => {
      component.selectedReleaseVersion = 123;
      component.releaseVersionId = 456;
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(true);

      (component as any).toggleAssignButton();

      expect(deviceOperationServiceSpy.shouldDisableAssignButton).toHaveBeenCalledWith(123, 456);
      expect(component.btnReleaseVersionDisable).toBe(true);
    });
  });

  describe('changeReleaseVersion - edge cases', () => {
    it('should handle no matching item found', () => {
      component.releaseVersions = [
        { id: 456, itemNumber: 'ITEM456' },
        { id: 789, itemNumber: 'ITEM789' }
      ];
      component.releaseVersionId = 123;
      const mockEvent = { target: { value: '999' } }; // Non-existent ID
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(true);

      component.changeReleaseVersion(mockEvent);

      expect(component.selectedReleaseVersion).toBe(-1);
      expect(component.btnReleaseVersionDisable).toBe(true);
    });

    it('should handle multiple matching items', () => {
      component.releaseVersions = [
        { id: 456, itemNumber: 'ITEM456' },
        { id: 456, itemNumber: 'ITEM456_DUPLICATE' }
      ];
      component.releaseVersionId = 123;
      const mockEvent = { target: { value: '456' } };
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(false);

      component.changeReleaseVersion(mockEvent);

      expect(component.selectedReleaseVersion).toBe(-1); // Should be -1 when multiple items found
      expect(component.btnReleaseVersionDisable).toBe(false);
    });
  });

  describe('assignReleaseVersion - edge cases', () => {
    it('should return early when validation fails', async () => {
      component.deviceDetailResponse = { id: 123 } as any;
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(false);

      await component.assignReleaseVersion();

      expect(deviceOperationServiceSpy.assignReleaseVersion).not.toHaveBeenCalled();
    });

    it('should handle assignReleaseVersion error', async () => {
      component.deviceDetailResponse = { id: 123 } as any;
      component.selectedReleaseVersion = 456;
      component.deviceIdInput = 123;
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.assignReleaseVersion.and.returnValue(Promise.reject(new Error('Assignment failed')));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      try {
        await component.assignReleaseVersion();
      } catch (error) {
        // Expected to throw
      }

      expect(component.loading).toBe(false);
    });
  });

  describe('deviceDetailModel - comprehensive', () => {
    it('should handle deviceDetailModel error', async () => {
      const mockDeviceId = 123;
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.reject(new Error('Load failed')));

      try {
        await component.deviceDetailModel(mockDeviceId);
      } catch (error) {
        // Expected to throw
      }

      expect(component.loading).toBe(false);
    });

    it('should handle transfer order device type', async () => {
      const mockDeviceId = 123;
      const mockDeviceDetail = {
        id: 123,
        deviceId: 'DEV001',
        deviceType: deviceTypesEnum.TEST_DEVICE,
        countryId: 1,
        packageVersion: '1.0.0',
        orderRecordType: 'TRANSFER_ORDER'
      } as any;
      const mockResult = {
        success: true,
        deviceDetail: mockDeviceDetail,
        releaseVersionId: 789,
        transferProductDetails: null
      };
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.resolve(mockResult));
      spyOn(component as any, 'getReleaseVersions').and.returnValue(Promise.resolve());

      await component.deviceDetailModel(mockDeviceId);

      expect(commonOperationsServiceSpy.accessDeviceListOperations).toHaveBeenCalledWith(false, false, component.resource);
    });
  });

  describe('ngOnDestroy - edge cases', () => {
    it('should handle undefined subscription gracefully', () => {
      component.subscriptionForCommonloading = undefined;
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });



  describe('validateProductStatusForDisableAction - comprehensive', () => {
    it('should call disableProductStatusForDevice directly', async () => {
      spyOn(component as any, 'disableProductStatusForDevice').and.returnValue(Promise.resolve());
      await (component as any).validateProductStatusForDisableAction();
      expect((component as any).disableProductStatusForDevice).toHaveBeenCalled();
    });
  });

  describe('enableDisableDevice - comprehensive', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, editable: false } as any;
      component.deviceIdInput = 123;
    });

    it('should call enableDisableDevices service', async () => {
      deviceOperationServiceSpy.enableDisableDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await (component as any).enableDisableDevice(true);

      expect(deviceOperationServiceSpy.enableDisableDevices).toHaveBeenCalledWith([123], [component.deviceDetailResponse], true, jasmine.any(String));
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });

    it('should handle enableDisableDevices failure', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.enableDisableDevices.and.returnValue(Promise.resolve(false));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await (component as any).enableDisableDevice(true);

      expect(component.deviceDetailModel).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('lockUnlock - comprehensive', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, locked: false } as any;
      component.deviceIdInput = 123;
    });

    it('should call lockUnlockDevices service', async () => {
      deviceOperationServiceSpy.lockUnlockDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await component.lockUnlock(true);

      expect(deviceOperationServiceSpy.lockUnlockDevices).toHaveBeenCalledWith([123], [component.deviceDetailResponse], true, jasmine.any(String));
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });

    it('should handle lockUnlockDevices failure', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.lockUnlockDevices.and.returnValue(Promise.resolve(false));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await component.lockUnlock(true);

      expect(component.deviceDetailModel).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('convertDataToTest - comprehensive', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, deviceType: deviceTypesEnum.CLIENT_DEVICE } as any;
      component.deviceIdInput = 123;
    });

    it('should handle convertDevicesToTest failure', async () => {
      deviceOperationServiceSpy.convertDevicesToTest.and.returnValue(Promise.resolve(false));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await component.convertDataToTest();

      expect(component.deviceDetailModel).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('convertDataToDemo - comprehensive', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, deviceType: deviceTypesEnum.CLIENT_DEVICE } as any;
      component.deviceIdInput = 123;
    });

    it('should handle convertDevicesToDemo failure', async () => {
      deviceOperationServiceSpy.convertDevicesToDemo.and.returnValue(Promise.resolve(false));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await component.convertDataToDemo();

      expect(component.deviceDetailModel).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('convertDataToClient - comprehensive', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123, deviceType: deviceTypesEnum.TEST_DEVICE } as any;
      component.deviceIdInput = 123;
    });

    it('should call convertDevicesToClient service', async () => {
      deviceOperationServiceSpy.convertDevicesToClient.and.returnValue(Promise.resolve(true));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await component.convertDataToClient();

      expect(deviceOperationServiceSpy.convertDevicesToClient).toHaveBeenCalledWith([123], [component.deviceDetailResponse], jasmine.any(String));
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });

    it('should handle convertDevicesToClient failure', async () => {
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.convertDevicesToClient.and.returnValue(Promise.resolve(false));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await component.convertDataToClient();

      expect(component.deviceDetailModel).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });



  describe('disableProductStatusForDevice - comprehensive', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123 } as any;
      component.deviceIdInput = 123;
    });

    it('should handle dialog dismissal', async () => {
      const mockConfig = { title: 'Confirm', message: 'Are you sure?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForDisableAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.reject(new Error('Dialog dismissed')));

      await (component as any).disableProductStatusForDevice();

      expect(deviceOperationServiceSpy.disableProductStatusForDevices).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });

    it('should handle disableProductStatusForDevices failure', async () => {
      const mockConfig = { title: 'Confirm', message: 'Are you sure?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForDisableAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(true));
      deviceOperationServiceSpy.disableProductStatusForDevices.and.returnValue(Promise.resolve(false));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await (component as any).disableProductStatusForDevice();

      expect(component.deviceDetailModel).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('rmaProductStatusForDevice - comprehensive', () => {
    beforeEach(() => {
      component.deviceDetailResponse = { id: 123 } as any;
      component.deviceIdInput = 123;
    });

    it('should handle dialog dismissal', async () => {
      const mockConfig = { title: 'Confirm RMA', message: 'Set to RMA?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForRMAAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.reject(new Error('Dialog dismissed')));

      await (component as any).rmaProductStatusForDevice();

      expect(deviceOperationServiceSpy.rmaProductStatusForDevices).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });

    it('should handle rmaProductStatusForDevices failure', async () => {
      const mockConfig = { title: 'Confirm RMA', message: 'Set to RMA?', btnOkText: 'OK', btnCancelText: 'Cancel' };
      confirmDialogServiceSpy.getBasicModelConfigForRMAAction.and.returnValue(mockConfig);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(true));
      deviceOperationServiceSpy.rmaProductStatusForDevices.and.returnValue(Promise.resolve(false));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      await (component as any).rmaProductStatusForDevice();

      expect(component.deviceDetailModel).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('ngOnInit - comprehensive', () => {
    it('should handle countryCacheService error', async () => {
      countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.reject(new Error('Cache error')));
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      try {
        await component.ngOnInit();
      } catch (error: any) {
        expect(error.message).toBe('Cache error');
      }

      expect(component.loading).toBe(false); // Should still be loading due to error
    });

    it('should initialize with all required properties', async () => {
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      spyOn(component as any, 'subjectInit').and.stub();

      await component.ngOnInit();

      expect(component.updateDeviceTypePermission).toBe(true);
      expect(component.productStatusList).toEqual([]);
      expect(component.lockUnlockStatus).toEqual([]);
      expect(component.editEnableDisableStatus).toEqual([]);
      expect(component.deviceDetailDisplay).toBe(true);
    });
  });

  describe('subjectInit - comprehensive', () => {
    it('should handle subscription with loading updates', () => {
      const mockSubject = new Subject<boolean>();
      commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(mockSubject);

      (component as any).subjectInit();

      // Test loading state changes
      mockSubject.next(true);
      expect(component.loading).toBe(true);

      mockSubject.next(false);
      expect(component.loading).toBe(false);
    });

    it('should handle undefined subject gracefully', () => {
      commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(undefined);

      expect(() => (component as any).subjectInit()).not.toThrow();
    });
  });

  describe('constructor and property initialization', () => {
    it('should initialize all string properties correctly', () => {
      expect(component.backBtnText).toBeDefined();
      expect(component.serialNo).toBeDefined();
      expect(component.hwId).toBeDefined();
      expect(component.salesOrderNumber).toBeDefined();
      expect(component.deviceType).toBeDefined();
      expect(component.country).toBeDefined();
      expect(component.systemSwVerstion).toBeDefined();
      expect(component.connectionState).toBeDefined();
      expect(component.customerName).toBeDefined();
      expect(component.locked).toBeDefined();
      expect(component.editable).toBeDefined();
      expect(component.status).toBeDefined();
      expect(component.lastCheckinDataAndType).toBeDefined();
      expect(component.macAddress).toBeDefined();
      expect(component.createDateAndTime).toBeDefined();
      expect(component.modifyDateAndTime).toBeDefined();
      expect(component.timeZone).toBeDefined();
      expect(component.probeVersion).toBeDefined();
      expect(component.handleVersion).toBeDefined();
      expect(component.pimsVersion).toBeDefined();
      expect(component.settingVersion).toBeDefined();
      expect(component.upsVersion).toBeDefined();
      expect(component.appVersion).toBeDefined();
      expect(component.jsonVersion).toBeDefined();
      expect(component.orderRecordType).toBeDefined();
      expect(component.partNo).toBeDefined();
      expect(component.po_no).toBeDefined();
      expect(component.customerEmail).toBeDefined();
      expect(component.releaseVersion).toBeDefined();
      expect(component.assignReleaseVersionBtnText).toBeDefined();
    });

    it('should initialize default values correctly', () => {
      expect(component.deviceDetailResponse).toBeNull();
      expect(component.loading).toBe(true);
      expect(component.releaseVersions).toEqual([]);
      expect(component.selectedReleaseVersion).toBe(-1);
      expect(component.btnReleaseVersionDisable).toBe(true);
      expect(component.deviceDetailDisplay).toBe(true);
      expect(component.transferOrderSelectionDisaplay).toBe(false);
      expect(component.page).toBe(0);
      expect(component.deviceOperations).toEqual([]);
      expect(component.updateDeviceTypePermission).toBe(true);
      expect(component.productStatusList).toEqual([]);
      expect(component.lockUnlockStatus).toEqual([]);
      expect(component.editEnableDisableStatus).toEqual([]);
      expect(component.deviceDetailsTrasferProduct).toBeNull();
    });
  });
});
