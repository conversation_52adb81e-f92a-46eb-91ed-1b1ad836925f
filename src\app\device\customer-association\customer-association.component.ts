import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { isNullOrUndefined } from 'is-what';
import { CountryValidMessage, EMAIL_VALIDATION_PATTERN, EnterCustomerName, EnterEmail, EnterValidEmail, OREDER_RECORD_TYPE_MANDATORY, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { BasicSalesOrderDetailResponse } from 'src/app/model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { CustomerAssociationModelRequest } from 'src/app/model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from 'src/app/model/customer-association-request';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';

@Component({
  selector: 'app-customer-association',
  templateUrl: './customer-association.component.html',
  styleUrls: ['./customer-association.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class CustomerAssociationComponent implements OnInit {

  @Input() customerAssociationModelRequest: CustomerAssociationModelRequest;

  loading = false;
  isButtonDisable: boolean = true;
  isDisable: boolean = false;
  //Text box max limit set
  textBoxMaxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  small_textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;
  enterCustomerName: string = EnterCustomerName;
  enterValidEmail: string = EnterValidEmail;
  enterEmail: string = EnterEmail;
  countryValidMessage: string = CountryValidMessage;
  orderRecordTypeMessage: string = OREDER_RECORD_TYPE_MANDATORY;


  requiredValidator: ValidatorFn = Validators.required;
  maxLengthValidator: ValidatorFn = Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH);
  emailValidatorPattern: ValidatorFn = Validators.pattern(EMAIL_VALIDATION_PATTERN);

  form = new FormGroup({
    salesOrderNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH)]),
    manualSalesOrderNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH)]),
    customerName: new FormControl(null, [this.requiredValidator, this.maxLengthValidator, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    poNumber: new FormControl(null, [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH)]),
    customerEmail: new FormControl(null, [this.emailValidatorPattern, this.maxLengthValidator]),
    country: new FormControl([], [Validators.required]),
    orderRecordType: new FormControl([], [Validators.required]),
    deviceAutoLock: new FormControl(false, []),
    probeAutoLock: new FormControl(false, []),
  });

  //multiselect dropdown
  dropdownSettings: MultiSelectDropdownSettings;
  countryDropDownSetting: MultiSelectDropdownSettings;
  orderRecordTypeSetting: MultiSelectDropdownSettings = null;
  isSalesOrderDisabled: boolean = false;
  salesOrderNumberList: string[] = [];
  countryList: Array<CountryListResponse> = [];
  orderRecordTypeList: Array<string> = [];

  constructor(
    private activeModal: NgbActiveModal,
    private commonsService: CommonsService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private exceptionService: ExceptionHandlingService,
    private commonOperationsService: CommonOperationsService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private countryCacheService: CountryCacheService,
  ) { }

  ngOnInit() {
    this.setInitialData();
  }

  /**
   * set loading
   * @param status 
   */
  private setLoading(status: boolean): void {
    this.commonOperationsService.callCommonLoadingSubject(status);
  }

  /**
   * get sales order number list and set customer detail
   * 
   * <AUTHOR>
   */
  public async setInitialData(): Promise<void> {

    this.setOrRemoveValidator(this.form.get("salesOrderNumber"), true, SMALL_TEXTBOX_MAX_LENGTH);
    this.dropdownSettings = this.multiSelectDropDownSettingService.getSalesOrderNumberDrpSetting(true, null, null, false);
    this.setLoading(true);
    this.countryDropDownSetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(true, false);
    this.orderRecordTypeSetting = this.multiSelectDropDownSettingService.getCreateOrderRecordTypeDrpSetting();
    this.countryList = await this.countryCacheService.getCountryListFromCache();
    this.orderRecordTypeList = await this.salesOrderApiCallService.getOrderRecordNumberList(false);
    this.salesOrderNumberList = await this.salesOrderApiCallService.getSalesOrderNumberList();
    this.setLoading(false);
    this.setCustomerDetailToForm();
  }


  /**
   * set customer details to form fields
   */
  private setCustomerDetailToForm(): void {
    if (isNullOrUndefined(this.customerAssociationModelRequest.salesOrderNumber)) {
      this.getSalesOrderDetailSuccess(null, null, null, null, false, false, null);
    }
    else {
      this.onDropdownSelect(this.customerAssociationModelRequest.salesOrderNumber);
      this.setValueAndFieldValidation([this.customerAssociationModelRequest?.salesOrderNumber], 'salesOrderNumber');
    }

    this.setDisableStatus();
  }

  /**
   * set value and add field validation
   * @param selectedValue 
   * @param controlName 
   */
  private setValueAndFieldValidation(selectedValue: any, controlName: string): void {
    if (!isNullOrUndefined(selectedValue) && selectedValue != "") {
      this.form.get(controlName).setValue(selectedValue);
      let control = this.form.get(controlName);
      control.setValidators([this.requiredValidator, this.commonsService.removeSpacesThrowError(), this.maxLengthValidator]);
      control.updateValueAndValidity();
    }
  }

  /**
   * close customer association popup
   */
  public decline(): void {
    this.activeModal.close(false);
  }

  /**
   * submit customer association form
   */
  public accept(): void {
    let salesOrderNumber: string = this.commonsService.checkNullFieldValue(!isNullOrUndefined(this.form.value.salesOrderNumber) ? this.form.value.salesOrderNumber[0] : this.form.value.manualSalesOrderNumber);
    let isSalesOrderNewAdd: boolean = !isNullOrUndefined(this.form.value.manualSalesOrderNumber);
    let country = this.commonsService.checkNullFieldValue(this.form.value.country);
    let orderRecordType = this.commonsService.checkNullFieldValue(this.form.value.orderRecordType);
    let basicSalesOrderDetailResponse: BasicSalesOrderDetailResponse = new BasicSalesOrderDetailResponse(salesOrderNumber,
      this.commonsService.checkNullFieldValue(this.form.value.customerName),
      this.commonsService.checkNullFieldValue(this.form.value.customerEmail),
      (country != null && country.length == 1) ? country[0].id : [],
      this.commonsService.checkNullFieldValue(this.form.value.poNumber),
      this.form.value.deviceAutoLock,
      this.form.value.probeAutoLock,
      orderRecordType.length > 0 ? orderRecordType[0] : null
    );

    let datsetdialog: CustomerAssociationRequest = new CustomerAssociationRequest(true, isSalesOrderNewAdd, basicSalesOrderDetailResponse);
    this.activeModal.close(datsetdialog);
  }

  /**
   * open sales order number text field
   */
  public openManualSalesOrderField(): void {
    this.isSalesOrderDisabled = true;
    document.getElementById('salesOrderField').style.display = 'block';
    document.getElementById('addSalesOrderBtn').setAttribute('disabled', 'disabled');
    this.form.reset();
    this.isDisable = false;
    this.setOrRemoveValidator(this.form.get("salesOrderNumber"), false, SMALL_TEXTBOX_MAX_LENGTH);
    this.setOrRemoveValidator(this.form.get("manualSalesOrderNumber"), true, SMALL_TEXTBOX_MAX_LENGTH);
    this.setDisableStatus();
  }

  /**
   * close sales order number text field
   */
  public closeManualSalesOrderField(): void {
    this.isSalesOrderDisabled = false;
    document.getElementById('salesOrderField').style.display = 'none';
    document.getElementById('addSalesOrderBtn').removeAttribute('disabled');
    this.form.reset();
    this.isDisable = false;
    this.setOrRemoveValidator(this.form.get("manualSalesOrderNumber"), false, SMALL_TEXTBOX_MAX_LENGTH);
    this.setOrRemoveValidator(this.form.get("salesOrderNumber"), true, SMALL_TEXTBOX_MAX_LENGTH);
  }

  /**
   * set or remove validator of form field
   * @param control 
   * @param isRequired 
   */
  private setOrRemoveValidator(control: AbstractControl, isRequired: boolean, maxLength: number): void {
    let validators = [Validators.maxLength(maxLength), this.commonsService.removeSpacesThrowError()];
    if (isRequired) {
      validators.push(this.requiredValidator);
    }
    control.setValidators(validators);
    control.updateValueAndValidity();
  }

  /**
   * field mark as touched
   * @param fieldName 
   */
  public onItemClickValidation(fieldName: string): void {
    if (this.form.invalid) {
      this.form.get(fieldName).markAsTouched();
    }
  }

  /**
   * select sales order value from dropdown list
   * 
   * <AUTHOR>
   * @param value 
   */
  public onDropdownSelect(value): void {
    this.setLoading(true);
    this.salesOrderApiCallService.getBasicSalesOrderDetails(value).subscribe({
      next: (res: HttpResponse<BasicSalesOrderDetailResponse>) => {
        if (res.status == 200 && !isNullOrUndefined(res.body)) {
          this.isDisable = res.body.salesForceOrder;
          this.getSalesOrderDetailSuccess(res.body.customerName, res.body.customerEmail, res.body.countryId, res.body.poNumber, res.body.deviceAutoLock, res.body.probeAutoLock, res.body.orderRecordType);
        } else {
          this.getSalesOrderDetailSuccess(null, null, null, null, false, false, null);
        }
      },
      error: (error: HttpErrorResponse) => {
        this.getSalesOrderDetailError(error);
      }
    });
  }

  /**
   * Get Sales order Success part
   * 
   * <AUTHOR>
   * @param customerName 
   * @param customerEmail 
   * @param poNumber 
   */
  private getSalesOrderDetailSuccess(customerName: string, customerEmail: string, countryId: number, poNumber: string, deviceAutoLock: boolean, probeAutoLock: boolean, orderRecordType: string): void {
    this.form.get("customerName").setValue(customerName);
    this.form.get("poNumber").setValue(poNumber);
    this.form.get("customerEmail").setValue(customerEmail);
    let countrys = this.commonsService.getDropDownValue(this.countryList, [countryId]);
    this.form.get("country").setValue(countrys);
    if (!isNullOrUndefined(this.form.get("salesOrderNumber").value)) {
      this.form.get("country").markAsTouched();
    }
    this.form.get("orderRecordType").setValue(isNullOrUndefined(orderRecordType) ? null : [orderRecordType]);
    this.form.get("deviceAutoLock").setValue(deviceAutoLock);
    this.form.get("probeAutoLock").setValue(probeAutoLock);
    this.setDisableStatus();
    this.setLoading(false);
  }

  /**
  * Get Sales order error part
  * 
  * <AUTHOR>
  * @param error 
  */
  private getSalesOrderDetailError(error: HttpErrorResponse): void {
    this.setLoading(false);
    this.exceptionService.customErrorMessage(error);
  }

  /**
   * unselect sales order value from dropdown list
   */
  public onDropdownDeSelect(): void {
    this.form.reset();
    this.isDisable = false;
    this.setDisableStatus();
  }

  /**
   * set disable button status
   */
  public setDisableStatus(): void {
    let salesOrderNumberIsNull = (this.commonsService.checkValueIsNullOrEmpty(this.form.get('salesOrderNumber').value) &&
      this.commonsService.checkValueIsNullOrEmpty(this.form.get('manualSalesOrderNumber').value))
    let customerNameIsNull = this.commonsService.checkValueIsNullOrEmpty(this.form.get('customerName').value);
    this.isButtonDisable = salesOrderNumberIsNull || customerNameIsNull;
  }

}
